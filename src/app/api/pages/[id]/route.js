import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Page } from '@/models/Page';
import mongoose from 'mongoose';

// GET - Fetch single page by ID or section name
export async function GET(request, { params }) {
  try {
    await connectDB();
    
    const { id } = await params;
    
    let page;
    
    // Try to find by MongoDB ObjectId first
    if (mongoose.Types.ObjectId.isValid(id)) {
      page = await Page.findById(id).lean();
    }
    
    // If not found by ID, try to find by section name
    if (!page) {
      page = await Page.findOne({ section: id }).lean();
    }
    
    if (!page) {
      return NextResponse.json(
        { success: false, message: 'Page not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: page
    });

  } catch (error) {
    console.error('Error fetching page:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch page', error: error.message },
      { status: 500 }
    );
  }
}

// PUT - Update single page (complete replacement)
export async function PUT(request, { params }) {
  try {
    await connectDB();
    
    const { id } = await params;
    const body = await request.json();
    
    let page;
    
    // Try to find by MongoDB ObjectId first
    if (mongoose.Types.ObjectId.isValid(id)) {
      page = await Page.findById(id);
    }
    
    // If not found by ID, try to find by section name
    if (!page) {
      page = await Page.findOne({ section: id });
    }
    
    if (!page) {
      return NextResponse.json(
        { success: false, message: 'Page not found' },
        { status: 404 }
      );
    }

    // Update all fields
    Object.assign(page, body);
    const updatedPage = await page.save();

    return NextResponse.json({
      success: true,
      message: 'Page updated successfully',
      data: updatedPage
    });

  } catch (error) {
    console.error('Error updating page:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update page', error: error.message },
      { status: 500 }
    );
  }
}

// PATCH - Partial update single page
export async function PATCH(request, { params }) {
  try {
    await connectDB();
    
    const { id } = await params;
    const body = await request.json();
    
    let page;
    
    // Try to find by MongoDB ObjectId first
    if (mongoose.Types.ObjectId.isValid(id)) {
      page = await Page.findById(id);
    }
    
    // If not found by ID, try to find by section name
    if (!page) {
      page = await Page.findOne({ section: id });
    }
    
    if (!page) {
      return NextResponse.json(
        { success: false, message: 'Page not found' },
        { status: 404 }
      );
    }

    // Handle specific operations
    if (body.operation) {
      switch (body.operation) {
        case 'addSecondaryEntry':
          if (body.secondaryEntry) {
            page.secondaryEntries.push(body.secondaryEntry);
          }
          break;
          
        case 'updateSecondaryEntry':
          if (body.entryId && body.secondaryEntry) {
            const entryIndex = page.secondaryEntries.findIndex(
              entry => entry._id.toString() === body.entryId
            );
            if (entryIndex !== -1) {
              Object.assign(page.secondaryEntries[entryIndex], body.secondaryEntry);
            }
          }
          break;
          
        case 'removeSecondaryEntry':
          if (body.entryId) {
            page.secondaryEntries = page.secondaryEntries.filter(
              entry => entry._id.toString() !== body.entryId
            );
          }
          break;
          
        case 'addTestimonial':
          if (body.testimonial) {
            // Check for unique name
            const existingNames = page.testimonials.map(t => t.name.toLowerCase());
            if (!existingNames.includes(body.testimonial.name.toLowerCase())) {
              page.testimonials.push(body.testimonial);
            } else {
              return NextResponse.json(
                { success: false, message: 'Testimonial name must be unique' },
                { status: 400 }
              );
            }
          }
          break;
          
        case 'updateTestimonial':
          if (body.testimonialId && body.testimonial) {
            const testimonialIndex = page.testimonials.findIndex(
              testimonial => testimonial._id.toString() === body.testimonialId
            );
            if (testimonialIndex !== -1) {
              // Check for unique name (excluding current testimonial)
              const existingNames = page.testimonials
                .filter((_, index) => index !== testimonialIndex)
                .map(t => t.name.toLowerCase());
              
              if (!existingNames.includes(body.testimonial.name.toLowerCase())) {
                Object.assign(page.testimonials[testimonialIndex], body.testimonial);
              } else {
                return NextResponse.json(
                  { success: false, message: 'Testimonial name must be unique' },
                  { status: 400 }
                );
              }
            }
          }
          break;
          
        case 'removeTestimonial':
          if (body.testimonialId) {
            page.testimonials = page.testimonials.filter(
              testimonial => testimonial._id.toString() !== body.testimonialId
            );
          }
          break;
          
        default:
          return NextResponse.json(
            { success: false, message: 'Invalid operation' },
            { status: 400 }
          );
      }
    } else {
      // Regular partial update
      Object.assign(page, body);
    }

    const updatedPage = await page.save();

    return NextResponse.json({
      success: true,
      message: 'Page updated successfully',
      data: updatedPage
    });

  } catch (error) {
    console.error('Error updating page:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update page', error: error.message },
      { status: 500 }
    );
  }
}

// DELETE - Delete single page
export async function DELETE(request, { params }) {
  try {
    await connectDB();
    
    const { id } = await params;
    
    let result;
    
    // Try to delete by MongoDB ObjectId first
    if (mongoose.Types.ObjectId.isValid(id)) {
      result = await Page.findByIdAndDelete(id);
    }
    
    // If not found by ID, try to delete by section name
    if (!result) {
      result = await Page.findOneAndDelete({ section: id });
    }
    
    if (!result) {
      return NextResponse.json(
        { success: false, message: 'Page not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Page deleted successfully',
      data: result
    });

  } catch (error) {
    console.error('Error deleting page:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete page', error: error.message },
      { status: 500 }
    );
  }
}
