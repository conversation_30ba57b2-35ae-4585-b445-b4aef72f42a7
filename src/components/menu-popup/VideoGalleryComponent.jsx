'use client'
import React from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useEffect, useState } from 'react'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import SpinerComponent from '../SpinerComponent'
import VideoPlayer from './VideoPlayer'
import Image from 'next/image'

function RoverOverButton({data,handleVideoClick}) {
  const [onhover,setOnHover]=useState(false)
  return(
    <div 
      onClick={()=>handleVideoClick(data)}
      onMouseEnter={()=>setOnHover(true)} 
      onMouseLeave={()=>setOnHover(false)} 
      className='z-10 absolute w-fit h-fit m-auto'
    >
        {onhover ? <ImageWrapperResponsive src={'assets/video_btn_ov.png'}/> :
        <ImageWrapperResponsive src={'assets/video_btn_off.png'}/>}
    </div>
  )
}

export default function VideoGalleryComponent() {
  const [error,setError]=useState('')
  const [showError,setShowError]=useState(false)
  const [loading,setLoading]=useState(false)
  const [data,setData]=useState(null)
  const [showVideoPlayer,setShowVideoPlayer]=useState(false) // show video player state
  const [videoData,setVideoData]=useState({}) // show video player state
  const {experienceState,disptachExperience}=useContextExperience()

  const fetchData = async (id) => {
    try {
      setLoading(true)
      const serverResponse=await fetch(`/api/video-gallery`)
      const responseData=await serverResponse.json()
      if(!data){
        setError('Failed to load data')
        setShowError(true)
      }
      // console.log(responseData?.data)
      setData(responseData?.data)
      setLoading(false)
      // return responseData
    } catch (error) {
      console.log(error)
      setError(error.message)
      setShowError(true)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const handleVideoClick = (item) => { 
    setShowVideoPlayer(true)
    setVideoData(item)
  }
  
  // console.log('VideoGalleryComponent:',videoData)
  
  return (
    <div className='flex mt-16 w-full h-fit text-white'>
      {loading 
        ? <SpinerComponent/>  
        : <div className='flex flex-col w-full h-full items-start justify-start'>
            <div className='flex w-full items-start'>
              <h1 className='text-7xl max-w-1/4 leading-14 mt-2'>HOME VIDEOS</h1>
              <div className='flex flex-col gap-2'>
                <span className='text-3xl'>Elephant Island offers a collection of home videos showcasing various experiences and moments.</span>
                <span className='text-sm'>Below is a series of videos presenting some of the activities and experiences that can be shared with families, Friends and loved ones.</span>
                <span className='text-sm'>Email your Home Video at <a className='underline' href="mailto:<EMAIL>">elephantislandbotswana.com</a> and hopefully your video will feature in the collection.</span>
              </div>
            </div>
            {/* <div className='grid md:grid-cols-2 grid-cols-1 mt-6 gap-4'> */}
            <div className='flex mt-4 flex-wrap w-full'>
              {data?.map((item) => (
                <div key={item._id} className='flex relative md:w-1/2 lg:w-1/3 h-80 flex-col items-center justify-center p-2'>
                  <div className='w-full h-full object-cover rounded-md'>
                    <div className='flex flex-col w-full h-52 items-center justify-center bg-gray-500 relative'>
                      {item?.thumbnail && <Image fill objectFit='cover' alt='video thumbnail' src={item?.thumbnail}/>}
                      <RoverOverButton data={item} handleVideoClick={handleVideoClick}/>
                    </div>
                    <div className='flex mt-4 flex-col w-full'>
                      <h1 className='text-xl uppercase font-bold'>
                        {item?.title}</h1>
                      <h1 className='text-sm'>
                        {item?.description || 'Lorem ipsum dolor sit amet consectetur, adipisicing elit. Nisi cupiditate eveniet optio eum iusto fugiat itaque corporis.'}
                      </h1>
                    </div>
                  </div>
                </div>
              ))} 
            </div>
          </div>
      }
      {showVideoPlayer && videoData && <VideoPlayer data={videoData} setShowVideoPlayer={setShowVideoPlayer}/>}
    </div>
  )
}
