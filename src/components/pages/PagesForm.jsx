'use client';

import React, { useState, useCallback, useMemo } from 'react';

const PagesForm = React.memo(({ page, onSave, onCancel, isLoading }) => {
  const [formData, setFormData] = useState(() => ({
    section: page?.section || 'the island',
    title: page?.title || '',
    body: page?.body || '',
    image: page?.image || '',
    url: page?.url || '',
    secondaryEntries: page?.secondaryEntries || [],
    testimonials: page?.testimonials || []
  }));

  const [errors, setErrors] = useState({});
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadingSecondaryImage, setUploadingSecondaryImage] = useState(null);
  const [showSecondaryForm, setShowSecondaryForm] = useState(false);
  const [secondaryFormData, setSecondaryFormData] = useState({
    title: '',
    body: '',
    image: ''
  });
  const [testimonialFormData, setTestimonialFormData] = useState({
    name: '',
    comment: ''
  });

  // Validation rules
  const validateForm = useCallback(() => {
    const newErrors = {};

    if (!formData.section) {
      newErrors.section = 'Section is required';
    }

    // Validation for sections that require title, body, and image
    if (['the island', 'experiences', 'location & contacts'].includes(formData.section)) {
      if (!formData.title?.trim()) {
        newErrors.title = 'Title is required';
      } else if (formData.title.length > 200) {
        newErrors.title = 'Title cannot exceed 200 characters';
      }

      if (!formData.body?.trim()) {
        newErrors.body = 'Body is required';
      } else if (formData.body.length > 5000) {
        newErrors.body = 'Body cannot exceed 5000 characters';
      }

      if (!formData.image?.trim()) {
        newErrors.image = 'Image is required';
      }
    }

    // URL validation for location & contacts
    if (formData.section === 'location & contacts') {
      if (!formData.url?.trim()) {
        newErrors.url = 'URL is required';
      } else {
        try {
          new URL(formData.url);
        } catch {
          newErrors.url = 'Please enter a valid URL';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle main form field changes
  const handleFieldChange = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  }, [errors]);

  // Handle image upload
  const handleImageUpload = useCallback(async (file, isSecondary = false, entryIndex = null) => {
    if (isSecondary) {
      setUploadingSecondaryImage(entryIndex !== null ? entryIndex : 'new');
    } else {
      setUploadingImage(true);
    }

    try {
      const formData = new FormData();
      formData.append('files', file);

      const response = await fetch('/api/upload/pages', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success && result.files?.[0]) {
        const imageUrl = result.files[0].url;
        
        if (isSecondary) {
          if (entryIndex !== null) {
            // Update existing secondary entry
            setFormData(prev => ({
              ...prev,
              secondaryEntries: prev.secondaryEntries.map((entry, index) =>
                index === entryIndex ? { ...entry, image: imageUrl } : entry
              )
            }));
          } else {
            // Update new secondary form
            setSecondaryFormData(prev => ({ ...prev, image: imageUrl }));
          }
        } else {
          handleFieldChange('image', imageUrl);
        }
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Failed to upload image: ' + error.message);
    } finally {
      if (isSecondary) {
        setUploadingSecondaryImage(null);
      } else {
        setUploadingImage(false);
      }
    }
  }, [handleFieldChange]);

  // Handle secondary entry operations
  const handleAddSecondaryEntry = useCallback(() => {
    if (!secondaryFormData.title || !secondaryFormData.body || !secondaryFormData.image) {
      alert('Please fill in all fields for the secondary entry');
      return;
    }

    setFormData(prev => ({
      ...prev,
      secondaryEntries: [...prev.secondaryEntries, { ...secondaryFormData }]
    }));

    setSecondaryFormData({ title: '', body: '', image: '' });
    setShowSecondaryForm(false);
  }, [secondaryFormData]);

  const handleRemoveSecondaryEntry = useCallback((index) => {
    setFormData(prev => ({
      ...prev,
      secondaryEntries: prev.secondaryEntries.filter((_, i) => i !== index)
    }));
  }, []);

  // Handle testimonial operations
  const handleAddTestimonial = useCallback(() => {
    if (!testimonialFormData.name || !testimonialFormData.comment) {
      alert('Please fill in both name and comment');
      return;
    }

    // Check for unique name
    const existingNames = formData.testimonials.map(t => t.name.toLowerCase());
    if (existingNames.includes(testimonialFormData.name.toLowerCase())) {
      alert('Testimonial name must be unique');
      return;
    }

    setFormData(prev => ({
      ...prev,
      testimonials: [...prev.testimonials, { ...testimonialFormData }]
    }));

    setTestimonialFormData({ name: '', comment: '' });
  }, [testimonialFormData, formData.testimonials]);

  const handleRemoveTestimonial = useCallback((index) => {
    setFormData(prev => ({
      ...prev,
      testimonials: prev.testimonials.filter((_, i) => i !== index)
    }));
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSave(formData);
    } catch (error) {
      console.error('Save error:', error);
    }
  }, [formData, validateForm, onSave]);

  // Memoized section options
  const sectionOptions = useMemo(() => [
    { value: 'the island', label: 'The Island' },
    { value: 'experiences', label: 'Experiences' },
    { value: 'testimonials', label: 'Testimonials' },
    { value: 'location & contacts', label: 'Location & Contacts' }
  ], []);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-trasandina-black text-gray-900 uppercase tracking-wide">
          {page ? 'Edit Page' : 'Create Page'}
        </h2>
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            form="pages-form"
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save Page'}
          </button>
        </div>
      </div>

      <form id="pages-form" onSubmit={handleSubmit} className="space-y-6">
        {/* Section Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Page Section *
          </label>
          <select
            value={formData.section}
            onChange={(e) => handleFieldChange('section', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled={!!page} // Disable editing section for existing pages
          >
            {sectionOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {errors.section && (
            <p className="mt-1 text-sm text-red-600">{errors.section}</p>
          )}
        </div>

        {/* Conditional rendering based on section */}
        {formData.section === 'testimonials' ? (
          <TestimonialsSection
            testimonials={formData.testimonials}
            testimonialFormData={testimonialFormData}
            setTestimonialFormData={setTestimonialFormData}
            onAddTestimonial={handleAddTestimonial}
            onRemoveTestimonial={handleRemoveTestimonial}
          />
        ) : (
          <StandardSection
            formData={formData}
            errors={errors}
            uploadingImage={uploadingImage}
            showSecondaryForm={showSecondaryForm}
            secondaryFormData={secondaryFormData}
            uploadingSecondaryImage={uploadingSecondaryImage}
            onFieldChange={handleFieldChange}
            onImageUpload={handleImageUpload}
            setShowSecondaryForm={setShowSecondaryForm}
            setSecondaryFormData={setSecondaryFormData}
            onAddSecondaryEntry={handleAddSecondaryEntry}
            onRemoveSecondaryEntry={handleRemoveSecondaryEntry}
          />
        )}
      </form>
    </div>
  );
});

// Testimonials Section Component
const TestimonialsSection = React.memo(({
  testimonials,
  testimonialFormData,
  setTestimonialFormData,
  onAddTestimonial,
  onRemoveTestimonial
}) => (
  <div className="space-y-6">
    <h3 className="text-lg font-medium text-gray-900">Testimonials Management</h3>

    {/* Add New Testimonial Form */}
    <div className="bg-gray-50 p-4 rounded-lg">
      <h4 className="text-md font-medium text-gray-800 mb-3">Add New Testimonial</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Name *
          </label>
          <input
            type="text"
            value={testimonialFormData.name}
            onChange={(e) => setTestimonialFormData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter testimonial author name"
            maxLength={100}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Comment *
          </label>
          <textarea
            value={testimonialFormData.comment}
            onChange={(e) => setTestimonialFormData(prev => ({ ...prev, comment: e.target.value }))}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter testimonial comment"
            maxLength={1000}
          />
        </div>
      </div>
      <button
        type="button"
        onClick={onAddTestimonial}
        disabled={!testimonialFormData.name || !testimonialFormData.comment}
        className="mt-3 px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Add Testimonial
      </button>
    </div>

    {/* Existing Testimonials */}
    {testimonials.length > 0 && (
      <div>
        <h4 className="text-md font-medium text-gray-800 mb-3">
          Existing Testimonials ({testimonials.length})
        </h4>
        <div className="space-y-3">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900">{testimonial.name}</h5>
                  <p className="text-gray-600 mt-1">{testimonial.comment}</p>
                </div>
                <button
                  type="button"
                  onClick={() => onRemoveTestimonial(index)}
                  className="ml-4 text-red-600 hover:text-red-800"
                >
                  Remove
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    )}
  </div>
));

// Standard Section Component (for island, experiences, location & contacts)
const StandardSection = React.memo(({
  formData,
  errors,
  uploadingImage,
  showSecondaryForm,
  secondaryFormData,
  uploadingSecondaryImage,
  onFieldChange,
  onImageUpload,
  setShowSecondaryForm,
  setSecondaryFormData,
  onAddSecondaryEntry,
  onRemoveSecondaryEntry
}) => (
  <div className="space-y-6">
    {/* Main Content Fields */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Title *
        </label>
        <input
          type="text"
          value={formData.title}
          onChange={(e) => onFieldChange('title', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Enter page title"
          maxLength={200}
        />
        {errors.title && (
          <p className="mt-1 text-sm text-red-600">{errors.title}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Main Image *
        </label>
        <div className="space-y-2">
          <input
            type="file"
            accept="image/*"
            onChange={(e) => e.target.files[0] && onImageUpload(e.target.files[0])}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled={uploadingImage}
          />
          {uploadingImage && (
            <p className="text-sm text-blue-600">Uploading image...</p>
          )}
          {formData.image && (
            <div className="mt-2">
              <img
                src={formData.image}
                alt="Preview"
                className="w-32 h-32 object-cover rounded-lg border border-gray-300"
              />
            </div>
          )}
        </div>
        {errors.image && (
          <p className="mt-1 text-sm text-red-600">{errors.image}</p>
        )}
      </div>
    </div>

    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Body Content *
      </label>
      <textarea
        value={formData.body}
        onChange={(e) => onFieldChange('body', e.target.value)}
        rows={6}
        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        placeholder="Enter page content"
        maxLength={5000}
      />
      <div className="flex justify-between mt-1">
        {errors.body && (
          <p className="text-sm text-red-600">{errors.body}</p>
        )}
        <p className="text-sm text-gray-500">
          {formData.body.length}/5000 characters
        </p>
      </div>
    </div>

    {/* URL field for location & contacts */}
    {formData.section === 'location & contacts' && (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          URL *
        </label>
        <input
          type="url"
          value={formData.url}
          onChange={(e) => onFieldChange('url', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="https://example.com"
        />
        {errors.url && (
          <p className="mt-1 text-sm text-red-600">{errors.url}</p>
        )}
      </div>
    )}

    {/* Secondary Entries for island and experiences */}
    {['the island', 'experiences'].includes(formData.section) && (
      <SecondaryEntriesSection
        secondaryEntries={formData.secondaryEntries}
        showSecondaryForm={showSecondaryForm}
        secondaryFormData={secondaryFormData}
        uploadingSecondaryImage={uploadingSecondaryImage}
        setShowSecondaryForm={setShowSecondaryForm}
        setSecondaryFormData={setSecondaryFormData}
        onImageUpload={onImageUpload}
        onAddSecondaryEntry={onAddSecondaryEntry}
        onRemoveSecondaryEntry={onRemoveSecondaryEntry}
      />
    )}
  </div>
));

// Secondary Entries Section Component
const SecondaryEntriesSection = React.memo(({
  secondaryEntries,
  showSecondaryForm,
  secondaryFormData,
  uploadingSecondaryImage,
  setShowSecondaryForm,
  setSecondaryFormData,
  onImageUpload,
  onAddSecondaryEntry,
  onRemoveSecondaryEntry
}) => (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-medium text-gray-900">Additional Content</h3>
      <button
        type="button"
        onClick={() => setShowSecondaryForm(!showSecondaryForm)}
        className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        {showSecondaryForm ? 'Cancel' : 'Add Additional Content'}
      </button>
    </div>

    {/* Add Secondary Entry Form */}
    {showSecondaryForm && (
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-md font-medium text-gray-800 mb-3">Add Additional Content</h4>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Title *
              </label>
              <input
                type="text"
                value={secondaryFormData.title}
                onChange={(e) => setSecondaryFormData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter content title"
                maxLength={200}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Image *
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={(e) => e.target.files[0] && onImageUpload(e.target.files[0], true)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                disabled={uploadingSecondaryImage === 'new'}
              />
              {uploadingSecondaryImage === 'new' && (
                <p className="text-sm text-blue-600 mt-1">Uploading image...</p>
              )}
              {secondaryFormData.image && (
                <div className="mt-2">
                  <img
                    src={secondaryFormData.image}
                    alt="Preview"
                    className="w-24 h-24 object-cover rounded-lg border border-gray-300"
                  />
                </div>
              )}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Body Content *
            </label>
            <textarea
              value={secondaryFormData.body}
              onChange={(e) => setSecondaryFormData(prev => ({ ...prev, body: e.target.value }))}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter content body"
              maxLength={5000}
            />
          </div>
          <button
            type="button"
            onClick={onAddSecondaryEntry}
            disabled={!secondaryFormData.title || !secondaryFormData.body || !secondaryFormData.image}
            className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Add Content
          </button>
        </div>
      </div>
    )}

    {/* Existing Secondary Entries */}
    {secondaryEntries.length > 0 && (
      <div>
        <h4 className="text-md font-medium text-gray-800 mb-3">
          Existing Additional Content ({secondaryEntries.length})
        </h4>
        <div className="space-y-4">
          {secondaryEntries.map((entry, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <h5 className="font-medium text-gray-900 mb-1">{entry.title}</h5>
                    <p className="text-sm text-gray-600">{entry.body.substring(0, 100)}...</p>
                  </div>
                  <div>
                    {entry.image && (
                      <img
                        src={entry.image}
                        alt={entry.title}
                        className="w-20 h-20 object-cover rounded-lg border border-gray-300"
                      />
                    )}
                  </div>
                  <div className="flex items-center justify-end">
                    <button
                      type="button"
                      onClick={() => onRemoveSecondaryEntry(index)}
                      className="text-red-600 hover:text-red-800 text-sm font-medium"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )}
  </div>
));

TestimonialsSection.displayName = 'TestimonialsSection';
StandardSection.displayName = 'StandardSection';
SecondaryEntriesSection.displayName = 'SecondaryEntriesSection';
PagesForm.displayName = 'PagesForm';

export default PagesForm;
