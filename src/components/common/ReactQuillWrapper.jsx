'use client';

import { useEffect, useRef, useState } from 'react';
import dynamic from 'next/dynamic';

// Polyfill for findDOMNode to fix React 19 compatibility
if (typeof window !== 'undefined') {
  const ReactDOM = require('react-dom');
  if (!ReactDOM.findDOMNode) {
    ReactDOM.findDOMNode = (instance) => {
      if (instance == null) return null;
      if (instance.nodeType === 1) return instance;
      if (instance._reactInternalFiber) {
        return ReactDOM.findDOMNode(instance._reactInternalFiber);
      }
      if (instance._reactInternalInstance) {
        return ReactDOM.findDOMNode(instance._reactInternalInstance);
      }
      // Fallback for React 19 - try to find the DOM node
      if (instance.current) {
        return instance.current;
      }
      return null;
    };
  }
}

// Dynamically import ReactQuill with the polyfill
const ReactQuill = dynamic(
  async () => {
    // Ensure polyfill is applied before importing
    const { default: RQ } = await import('react-quill');
    return RQ;
  },
  { 
    ssr: false,
    loading: () => (
      <div className="border border-gray-300 rounded-md p-3 min-h-[80px] bg-gray-50 animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    )
  }
);

export default function ReactQuillWrapper({ 
  value, 
  onChange, 
  placeholder, 
  modules, 
  formats, 
  theme = 'snow',
  style,
  className,
  ...props 
}) {
  const [mounted, setMounted] = useState(false);
  const quillRef = useRef(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className={`border border-gray-300 rounded-md p-3 min-h-[80px] bg-gray-50 ${className || ''}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
      </div>
    );
  }

  return (
    <ReactQuill
      ref={quillRef}
      theme={theme}
      value={value || ''}
      onChange={onChange}
      modules={modules}
      formats={formats}
      placeholder={placeholder}
      style={style}
      className={className}
      {...props}
    />
  );
}
