'use client';

import { useState, useRef } from 'react';
import { MdCloudUpload, MdVideoLibrary, MdDelete, MdCheckCircle, MdError } from 'react-icons/md';
import VideoMetadataModal from './VideoMetadataModal';

export default function MultipleVideoUpload({ onUploadComplete, onCancel }) {
  const [selectedVideos, setSelectedVideos] = useState([]);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [showMetadataModal, setShowMetadataModal] = useState(false);
  const [videoMetadataList, setVideoMetadataList] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({
    isUploading: false,
    currentVideo: 0,
    totalVideos: 0,
    uploadedVideos: [],
    failedVideos: []
  });
  const fileInputRef = useRef(null);

  const handleVideoSelection = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      setSelectedVideos(files);
      setCurrentVideoIndex(0);
      setVideoMetadataList([]);
      setShowMetadataModal(true);
    }
  };

  const handleMetadataSave = async (videoMetadata) => {
    const updatedMetadataList = [...videoMetadataList, videoMetadata];
    setVideoMetadataList(updatedMetadataList);
    
    // Check if we have metadata for all videos
    if (updatedMetadataList.length === selectedVideos.length) {
      // All metadata collected, start upload process
      setShowMetadataModal(false);
      await startVideoUploads(updatedMetadataList);
    } else {
      // Move to next video
      setCurrentVideoIndex(prev => prev + 1);
    }
  };

  const handleMetadataCancel = () => {
    setShowMetadataModal(false);
    setSelectedVideos([]);
    setVideoMetadataList([]);
    setCurrentVideoIndex(0);
  };

  const uploadVideo = async (videoFile) => {
    try {
      const formData = new FormData();
      formData.append('files', videoFile);
      
      const response = await fetch('/api/upload/video-gallery', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      
      if (result.success) {
        return result.url;
      } else {
        throw new Error(result.error || 'Video upload failed');
      }
    } catch (error) {
      console.error('Video upload error:', error);
      throw error;
    }
  };

  const createVideoGalleryItem = async (videoMetadata, videoUrl) => {
    try {
      const response = await fetch('/api/video-gallery', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: videoMetadata.title,
          description: videoMetadata.description,
          url: videoUrl,
          thumbnail: videoMetadata.thumbnail,
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.message || 'Failed to create video gallery item');
      }
    } catch (error) {
      console.error('Video gallery creation error:', error);
      throw error;
    }
  };

  const startVideoUploads = async (metadataList) => {
    setUploadProgress({
      isUploading: true,
      currentVideo: 0,
      totalVideos: metadataList.length,
      uploadedVideos: [],
      failedVideos: []
    });

    const uploadedVideos = [];
    const failedVideos = [];

    for (let i = 0; i < metadataList.length; i++) {
      const videoMetadata = metadataList[i];
      
      setUploadProgress(prev => ({
        ...prev,
        currentVideo: i + 1
      }));

      try {
        // Upload video file
        const videoUrl = await uploadVideo(videoMetadata.videoFile);
        
        // Create video gallery item
        const galleryItem = await createVideoGalleryItem(videoMetadata, videoUrl);
        
        uploadedVideos.push({
          ...galleryItem,
          originalFile: videoMetadata.videoFile
        });
        
        setUploadProgress(prev => ({
          ...prev,
          uploadedVideos: [...prev.uploadedVideos, galleryItem]
        }));
        
      } catch (error) {
        console.error(`Failed to upload video ${i + 1}:`, error);
        failedVideos.push({
          filename: videoMetadata.videoFile.name,
          title: videoMetadata.title,
          error: error.message
        });
        
        setUploadProgress(prev => ({
          ...prev,
          failedVideos: [...prev.failedVideos, {
            filename: videoMetadata.videoFile.name,
            title: videoMetadata.title,
            error: error.message
          }]
        }));
      }
    }

    // Upload complete
    setUploadProgress(prev => ({
      ...prev,
      isUploading: false
    }));

    // Notify parent component
    onUploadComplete({
      uploaded: uploadedVideos,
      failed: failedVideos,
      total: metadataList.length
    });
  };

  const removeVideo = (index) => {
    const updatedVideos = selectedVideos.filter((_, i) => i !== index);
    setSelectedVideos(updatedVideos);
    
    if (updatedVideos.length === 0) {
      setVideoMetadataList([]);
      setCurrentVideoIndex(0);
    }
  };

  const resetUpload = () => {
    setSelectedVideos([]);
    setVideoMetadataList([]);
    setCurrentVideoIndex(0);
    setUploadProgress({
      isUploading: false,
      currentVideo: 0,
      totalVideos: 0,
      uploadedVideos: [],
      failedVideos: []
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Show upload progress
  if (uploadProgress.isUploading || uploadProgress.uploadedVideos.length > 0 || uploadProgress.failedVideos.length > 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6">
          Upload Progress
        </h2>
        
        {uploadProgress.isUploading && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                Uploading video {uploadProgress.currentVideo} of {uploadProgress.totalVideos}
              </span>
              <span className="text-sm text-gray-500">
                {Math.round((uploadProgress.currentVideo / uploadProgress.totalVideos) * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(uploadProgress.currentVideo / uploadProgress.totalVideos) * 100}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Upload Results */}
        {!uploadProgress.isUploading && (
          <div className="space-y-4">
            {uploadProgress.uploadedVideos.length > 0 && (
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex items-center mb-2">
                  <MdCheckCircle className="h-5 w-5 text-green-600 mr-2" />
                  <h3 className="text-sm font-medium text-green-800">
                    Successfully Uploaded ({uploadProgress.uploadedVideos.length})
                  </h3>
                </div>
                <ul className="text-sm text-green-700 space-y-1">
                  {uploadProgress.uploadedVideos.map((video, index) => (
                    <li key={index}>• {video.title}</li>
                  ))}
                </ul>
              </div>
            )}

            {uploadProgress.failedVideos.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex items-center mb-2">
                  <MdError className="h-5 w-5 text-red-600 mr-2" />
                  <h3 className="text-sm font-medium text-red-800">
                    Failed Uploads ({uploadProgress.failedVideos.length})
                  </h3>
                </div>
                <ul className="text-sm text-red-700 space-y-1">
                  {uploadProgress.failedVideos.map((video, index) => (
                    <li key={index}>• {video.title}: {video.error}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
              <button
                onClick={resetUpload}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Upload More Videos
              </button>
              <button
                onClick={onCancel}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Done
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        Upload Multiple Videos
      </h2>
      
      {selectedVideos.length === 0 ? (
        // File selection
        <div className="text-center">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 hover:border-gray-400 transition-colors">
            <MdVideoLibrary className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="space-y-2">
              <label className="cursor-pointer">
                <span className="text-lg font-medium text-gray-900">
                  Select video files to upload
                </span>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="video/*"
                  onChange={handleVideoSelection}
                  className="hidden"
                />
              </label>
              <p className="text-sm text-gray-500">
                Choose multiple video files. You'll be asked to provide title, description, and thumbnail for each video.
              </p>
              <p className="text-xs text-gray-400">
                Supported formats: MP4, WebM, AVI, MOV. Maximum size: 100MB per video.
              </p>
            </div>
          </div>
          
          <div className="mt-6 flex justify-end">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
          </div>
        </div>
      ) : (
        // Selected videos list
        <div>
          <p className="text-sm text-gray-600 mb-4">
            Selected {selectedVideos.length} video{selectedVideos.length !== 1 ? 's' : ''}. 
            Provide metadata for each video to continue.
          </p>
          
          <div className="space-y-3 mb-6">
            {selectedVideos.map((video, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                <div className="flex items-center space-x-3">
                  <MdVideoLibrary className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{video.name}</p>
                    <p className="text-xs text-gray-500">
                      {(video.size / 1024 / 1024).toFixed(1)} MB
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => removeVideo(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <MdDelete className="h-5 w-5" />
                </button>
              </div>
            ))}
          </div>
          
          <div className="flex items-center justify-end space-x-4">
            <button
              onClick={() => {
                setSelectedVideos([]);
                setVideoMetadataList([]);
              }}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Clear Selection
            </button>
            <button
              onClick={() => setShowMetadataModal(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
            >
              <MdCloudUpload className="mr-2" />
              Continue with Upload
            </button>
          </div>
        </div>
      )}

      {/* Video Metadata Modal */}
      <VideoMetadataModal
        isOpen={showMetadataModal}
        onClose={handleMetadataCancel}
        videoFile={selectedVideos[currentVideoIndex]}
        onSave={handleMetadataSave}
        currentIndex={currentVideoIndex}
        totalVideos={selectedVideos.length}
      />
    </div>
  );
}
