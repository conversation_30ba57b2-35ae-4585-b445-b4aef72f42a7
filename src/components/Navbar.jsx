'use client'
import Link from 'next/link';
import { useSearchParams } from 'next/navigation'
import React, { useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience';
import ImageWrapperResponsive from './ImageWrapperResponsive';
import _360BookNowBtn from '../components/360s/_360BookNowBtn';
import { motion } from 'framer-motion';
import { HiOutlineMenuAlt3, HiX } from 'react-icons/hi';
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience';

export default function Navbar() {
    const searchParams = useSearchParams()
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const { experienceState, disptachExperience } = useContextExperience()
    const id = searchParams.get('id')
    const links=['the island','experiences','testimonials','location & contacts']

    const handlePageClick = (i,index) => {
      // console.log(i,index)
      0===index && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_ISLAND_PAGE})
      1===index && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_EXPERIENCE_PAGE})
      2===index && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_TESTIMONIALS_PAGE})
      3===index && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_LOCATION_AND_CONTACTS_PAGE})
    }
    
    // console.log('Navbar:',experienceState)

  return (
    <motion.nav 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      className='navbar flex absolute z-10 top-0 left-0 w-full h-28 items-start from-black bg-gradient-to-b'
    >
      <div className='flex relative w-full top-0 h-16 items-center justify-between'>
        <div className='flex items-center gap-10'>
          <Link href={'/'} className="flex bg-inherit object-left-top relative w-fit h-fit text-lg tracking-[6px]">
            <ImageWrapperResponsive className={'w-auto h-full'} src={'/assets/elephant_island_logo_white_for_nav_bar.png'} alt='elephant island logo'/>
          </Link>
          <div className='hidden md:flex cursor-pointer h-fit text-sm text-center uppercase mb-2 text-white gap-5 items-center'>
            {links.map((i,index)=><div onClick={()=>handlePageClick(i,index)} key={i}>{i}</div>)}
          </div>
        </div>
        <div className='hidden md:flex items-center w-fit h-full'>
          <_360BookNowBtn/>
        </div>
        {/* Mobile Menu Button */}
        <button
          className="md:hidden z-50 text-3xl mr-2"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          aria-label="Toggle menu"
        >
          {mobileMenuOpen ? <HiX /> : <HiOutlineMenuAlt3 />}
        </button>
      </div>
      

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="nabar-popup absolute z-40 top-0 left-0 w-full text-white h-svh bg-black/90 shadow-sm py-6 md:hidden">
          <div className="flex flex-col items-center mt-12 space-y-6 px-6">
            {links.map((link, index) => (
              <div
                key={index}
                className="text-xl uppercase tracking-widest font-extralight py-2"
                onClick={() => setMobileMenuOpen(false)}
              >
                {link}
              </div>
            ))}
            <_360BookNowBtn/>
          </div>
        </div>
      )}
    </motion.nav>
  )
}
