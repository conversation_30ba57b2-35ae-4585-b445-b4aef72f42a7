'use client';

import { useState, useEffect } from 'react';
import { MdSave, MdCancel, MdCloudUpload, MdAdd, MdDelete } from 'react-icons/md';
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';

// Dynamically import ReactQuill to avoid SSR issues
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });

// Quill editor configuration
const quillModules = {
  toolbar: [
    [{ 'header': [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'align': [] }],
    ['link'],
    ['clean']
  ],
};

const quillFormats = [
  'header', 'bold', 'italic', 'underline', 'strike',
  'list', 'bullet', 'color', 'background', 'align', 'link'
];

export default function InfoMarkerForm({
  infoMarker = null,
  onSave,
  onCancel,
  isLoading = false
}) {
  const [formData, setFormData] = useState({
    title: '',
    body1: '',
    body2: '',
    image: '',
    secondaryEntries: [],
  });
  const [errors, setErrors] = useState({});
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [uploading, setUploading] = useState(false);

  // Secondary entry form state
  const [showSecondaryForm, setShowSecondaryForm] = useState(false);
  const [secondaryFormData, setSecondaryFormData] = useState({
    image: '',
    title: '',
    body: '',
    body2: '',
  });
  const [secondaryImageFile, setSecondaryImageFile] = useState(null);
  const [secondaryImagePreview, setSecondaryImagePreview] = useState('');
  const [uploadingSecondaryImage, setUploadingSecondaryImage] = useState(false);
  const [submittingSecondaryEntry, setSubmittingSecondaryEntry] = useState(false);

  // Initialize form data when infoMarker prop changes
  useEffect(() => {
    if (infoMarker) {
      setFormData({
        title: infoMarker.title || '',
        body1: infoMarker.body1 || '',
        body2: infoMarker.body2 || '',
        image: infoMarker.image || '',
        secondaryEntries: infoMarker.secondaryEntries || [],
      });
      setImagePreview(infoMarker.image || '');
    } else {
      setFormData({
        title: '',
        body1: '',
        body2: '',
        image: '',
        secondaryEntries: [],
      });
      setImagePreview('');
    }
  }, [infoMarker]);

  // Handle Quill editor content changes
  const handleQuillChange = (content, fieldName) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: content
    }));

    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: ''
      }));
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async () => {
    if (!imageFile) return null;

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('files', imageFile);

      const response = await fetch('/api/upload/info-markers', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        // For single file uploads, the URL is directly in the response
        // For multiple file uploads, it's in result.data array
        if (result.url) {
          return result.url;
        } else if (result.data && result.data.length > 0) {
          return result.data[0].url;
        } else {
          throw new Error('No URL returned from upload');
        }
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  };

  // Secondary entry image upload
  const uploadSecondaryImage = async () => {
    if (!secondaryImageFile) return null;

    setUploadingSecondaryImage(true);
    try {
      const formData = new FormData();
      formData.append('files', secondaryImageFile);

      const response = await fetch('/api/upload/info-markers', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        // For single file uploads, the URL is directly in the response
        // For multiple file uploads, it's in result.data array
        if (result.url) {
          return result.url;
        } else if (result.data && result.data.length > 0) {
          return result.data[0].url;
        } else {
          throw new Error('No URL returned from upload');
        }
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Secondary image upload error:', error);
      throw error;
    } finally {
      setUploadingSecondaryImage(false);
    }
  };

  // Handle secondary entry image change
  const handleSecondaryImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSecondaryImageFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setSecondaryImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle secondary form input changes
  const handleSecondaryInputChange = (e) => {
    const { name, value } = e.target;
    setSecondaryFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Helper function to strip HTML tags and check if content is empty
  const isQuillContentEmpty = (content) => {
    if (!content) return true;
    // Remove HTML tags and check if there's actual text content
    const textContent = content.replace(/<[^>]*>/g, '').trim();
    return textContent === '' || textContent === '\n';
  };

  const validateForm = () => {
    const newErrors = {};

    if (isQuillContentEmpty(formData.title)) {
      newErrors.title = 'Title is required';
    }

    if (isQuillContentEmpty(formData.body1)) {
      newErrors.body1 = 'Body 1 is required';
    }

    if (isQuillContentEmpty(formData.body2)) {
      newErrors.body2 = 'Body 2 is required';
    }

    if (!formData.image && !imageFile) {
      newErrors.image = 'Image is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      let imageUrl = formData.image;
      
      // Upload new image if selected
      if (imageFile) {
        imageUrl = await uploadImage();
      }
      
      const submitData = {
        ...formData,
        image: imageUrl,
      };
      
      await onSave(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: 'Failed to save info marker. Please try again.' });
    }
  };

  // Submit secondary entry
  const handleSubmitSecondaryEntry = async () => {
    if (!infoMarker?._id) {
      setErrors({ secondary: 'Please save the main info marker first before adding secondary entries.' });
      return;
    }

    // Validate secondary entry
    if (!secondaryFormData.title || !secondaryFormData.body || !secondaryFormData.body2) {
      setErrors({ secondary: 'Please fill in all required fields for the secondary entry.' });
      return;
    }

    setSubmittingSecondaryEntry(true);
    try {
      let imageUrl = secondaryFormData.image;

      // Upload new image if selected
      if (secondaryImageFile) {
        imageUrl = await uploadSecondaryImage();
      }

      if (!imageUrl) {
        setErrors({ secondary: 'Please select an image for the secondary entry.' });
        return;
      }

      const secondaryEntry = {
        image: imageUrl,
        title: secondaryFormData.title,
        body: secondaryFormData.body,
        body2: secondaryFormData.body2,
      };

      const response = await fetch(`/api/info-markers/${infoMarker._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operation: 'addSecondaryEntry',
          secondaryEntry,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Update local form data
        setFormData(prev => ({
          ...prev,
          secondaryEntries: data.data.secondaryEntries,
        }));

        // Reset secondary form
        setSecondaryFormData({
          image: '',
          title: '',
          body: '',
          body2: '',
        });
        setSecondaryImageFile(null);
        setSecondaryImagePreview('');
        setShowSecondaryForm(false);
        setErrors({});
      } else {
        throw new Error(data.message || 'Failed to add secondary entry');
      }
    } catch (error) {
      console.error('Secondary entry submission error:', error);
      setErrors({ secondary: 'Failed to add secondary entry. Please try again.' });
    } finally {
      setSubmittingSecondaryEntry(false);
    }
  };

  // Remove secondary entry
  const handleRemoveSecondaryEntry = async (index) => {
    if (!infoMarker?._id) return;

    try {
      const response = await fetch(`/api/info-markers/${infoMarker._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operation: 'removeSecondaryEntry',
          entryIndex: index,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Update local form data
        setFormData(prev => ({
          ...prev,
          secondaryEntries: data.data.secondaryEntries,
        }));
      } else {
        throw new Error(data.message || 'Failed to remove secondary entry');
      }
    } catch (error) {
      console.error('Remove secondary entry error:', error);
      setErrors({ secondary: 'Failed to remove secondary entry. Please try again.' });
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <style jsx>{`
        .ql-editor {
          min-height: 80px;
        }
        .ql-editor.ql-blank::before {
          font-style: normal;
          color: #9ca3af;
        }
        .ql-toolbar {
          border-top: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-bottom: none;
        }
        .ql-container {
          border-bottom: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-top: none;
        }
      `}</style>
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        {infoMarker ? 'Edit Info Marker' : 'Create New Info Marker'}
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className='flex flex-col w-full gap-4'>
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <div className={`border rounded-md ${errors.title ? 'border-red-500' : 'border-gray-300'}`}>
              <ReactQuill
                theme="snow"
                value={formData.title}
                onChange={(content) => handleQuillChange(content, 'title')}
                modules={quillModules}
                formats={quillFormats}
                placeholder="Enter info marker title"
                style={{ minHeight: '80px' }}
              />
            </div>
            {errors.title && (
              <p className="mt-1 text-sm text-red-600">{errors.title}</p>
            )}
          </div>

          {/* Body 1 */}
          <div>
            <label htmlFor="body1" className="block text-sm font-medium text-gray-700 mb-2">
              Body 1 *
            </label>
            <div className={`border rounded-md ${errors.body1 ? 'border-red-500' : 'border-gray-300'}`}>
              <ReactQuill
                theme="snow"
                value={formData.body1}
                onChange={(content) => handleQuillChange(content, 'body1')}
                modules={quillModules}
                formats={quillFormats}
                placeholder="Enter first body content"
                style={{ minHeight: '120px' }}
              />
            </div>
            {errors.body1 && (
              <p className="mt-1 text-sm text-red-600">{errors.body1}</p>
            )}
          </div>

          {/* Body 2 */}
          <div>
            <label htmlFor="body2" className="block text-sm font-medium text-gray-700 mb-2">
              Body 2 *
            </label>
            <div className={`border rounded-md ${errors.body2 ? 'border-red-500' : 'border-gray-300'}`}>
              <ReactQuill
                theme="snow"
                value={formData.body2}
                onChange={(content) => handleQuillChange(content, 'body2')}
                modules={quillModules}
                formats={quillFormats}
                placeholder="Enter second body content"
                style={{ minHeight: '120px' }}
              />
            </div>
            {errors.body2 && (
              <p className="mt-1 text-sm text-red-600">{errors.body2}</p>
            )}
          </div>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Image *
            </label>
            
            {/* Image Preview */}
            {imagePreview && (
              <div className="mb-4">
                <img
                  src={imagePreview}
                  alt="Preview"
                  className="w-32 h-32 object-cover rounded-md border border-gray-300"
                />
              </div>
            )}
            
            {/* File Input */}
            <div className="flex items-center space-x-4">
              <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
                <MdCloudUpload className="mr-2" />
                Choose Image
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="hidden"
                />
              </label>
              
              {imageFile && (
                <span className="text-sm text-gray-600">
                  {imageFile.name}
                </span>
              )}
            </div>
            
            {errors.image && (
              <p className="mt-1 text-sm text-red-600">{errors.image}</p>
            )}
          </div>
        </div>

        {/* Secondary Entries Section */}
        {infoMarker && (
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">Additional Content</h3>
              <button
                type="button"
                onClick={() => setShowSecondaryForm(!showSecondaryForm)}
                className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
              >
                <MdAdd className="mr-2" />
                {showSecondaryForm ? 'Cancel' : 'Add Additional Entry'}
              </button>
            </div>

            {/* Secondary Entry Form */}
            {showSecondaryForm && (
              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <h4 className="text-md font-medium text-gray-800 mb-4">New Additional Entry</h4>

                <div className="space-y-4">
                  {/* Secondary Image Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Image *
                    </label>

                    {/* Secondary Image Preview */}
                    {secondaryImagePreview && (
                      <div className="mb-4">
                        <img
                          src={secondaryImagePreview}
                          alt="Secondary Preview"
                          className="w-32 h-32 object-cover rounded-md border border-gray-300"
                        />
                      </div>
                    )}

                    {/* Secondary File Input */}
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
                        <MdCloudUpload className="mr-2" />
                        Choose Image
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleSecondaryImageChange}
                          className="hidden"
                        />
                      </label>

                      {secondaryImageFile && (
                        <span className="text-sm text-gray-600">
                          {secondaryImageFile.name}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Secondary Title */}
                  <div>
                    <label htmlFor="secondaryTitle" className="block text-sm font-medium text-gray-700 mb-2">
                      Title *
                    </label>
                    <input
                      type="text"
                      id="secondaryTitle"
                      name="title"
                      value={secondaryFormData.title}
                      onChange={handleSecondaryInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter title for additional content"
                    />
                  </div>

                  {/* Secondary Body 1 */}
                  <div>
                    <label htmlFor="secondaryBody" className="block text-sm font-medium text-gray-700 mb-2">
                      Body 1 *
                    </label>
                    <textarea
                      id="secondaryBody"
                      name="body"
                      value={secondaryFormData.body}
                      onChange={handleSecondaryInputChange}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter first body content"
                    />
                  </div>

                  {/* Secondary Body 2 */}
                  <div>
                    <label htmlFor="secondaryBody2" className="block text-sm font-medium text-gray-700 mb-2">
                      Body 2 *
                    </label>
                    <textarea
                      id="secondaryBody2"
                      name="body2"
                      value={secondaryFormData.body2}
                      onChange={handleSecondaryInputChange}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter second body content"
                    />
                  </div>

                  {/* Submit Secondary Entry Button */}
                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={handleSubmitSecondaryEntry}
                      disabled={submittingSecondaryEntry || uploadingSecondaryImage}
                      className={`px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center ${
                        submittingSecondaryEntry || uploadingSecondaryImage ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                    >
                      <MdSave className="mr-2" />
                      {submittingSecondaryEntry || uploadingSecondaryImage ? 'Submitting...' : 'Submit Entry'}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Existing Secondary Entries */}
            {formData.secondaryEntries && formData.secondaryEntries.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-800 mb-3">
                  Existing Additional Content ({formData.secondaryEntries.length})
                </h4>
                <div className="space-y-4">
                  {formData.secondaryEntries.map((entry, index) => (
                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <h5 className="font-medium text-gray-900 mb-1">{entry.title}</h5>
                            <p className="text-sm text-gray-600">{entry.body.substring(0, 100)}...</p>
                            {entry.body2 && (
                              <p className="text-sm text-gray-500 mt-1">{entry.body2.substring(0, 50)}...</p>
                            )}
                          </div>
                          <div>
                            {entry.image && (
                              <img
                                src={entry.image}
                                alt={entry.title}
                                className="w-20 h-20 object-cover rounded-lg border border-gray-300"
                              />
                            )}
                          </div>
                          <div className="flex justify-end">
                            <button
                              type="button"
                              onClick={() => handleRemoveSecondaryEntry(index)}
                              className="p-2 text-red-600 hover:bg-red-100 rounded-md"
                              title="Remove Entry"
                            >
                              <MdDelete />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Secondary Entry Error */}
            {errors.secondary && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 mt-4">
                <p className="text-red-600 text-sm">{errors.secondary}</p>
              </div>
            )}
          </div>
        )}

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-600 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 flex items-center"
          >
            <MdCancel className="mr-2" />
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isLoading || uploading}
            className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center ${
              isLoading || uploading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <MdSave className="mr-2" />
            {isLoading || uploading ? 'Saving...' : 'Save Info Marker'}
          </button>
        </div>
      </form>
    </div>
  );
}
